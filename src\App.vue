<template>
  <div class="game-container">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <div class="score-section">
        <h3>个人成绩</h3>
        <div class="score">得分: {{ score }}</div>
        <div class="stats">
          <div>击落: {{ hitCount }}</div>
          <div>逃脱: {{ escapeCount }}</div>
        </div>
        <div class="accuracy">命中率: {{ accuracy }}%</div>
      </div>

      <div class="controls">
        <button @click="togglePause" :disabled="!gameStarted">
          {{ isPaused ? '▶️ 开始' : '⏸️ 暂停' }}
        </button>
        <button @click="restartGame">🔄 重新游戏</button>
      </div>

      <div class="word-bank-section">
        <h4>选择词库:</h4>
        <select v-model="selectedWordBank" @change="changeWordBank" :disabled="gameStarted && !isPaused">
          <option value="junior">初中英语</option>
          <option value="senior">高中英语</option>
          <option value="cet4">大学四级</option>
          <option value="cet6">大学六级</option>
          <option value="postgraduate">考研英语</option>
        </select>
      </div>
    </div>

    <!-- 中间游戏区域 -->
    <div class="game-area" @keydown="handleKeyDown" tabindex="0" ref="gameArea">
      <div class="game-header">
        <h1>单词雷电</h1>
        <div v-if="!gameStarted" class="start-message">
          按空格键开始游戏
        </div>
        <div v-if="gameOver" class="game-over">
          <h2>游戏结束!</h2>
          <p>最终得分: {{ score }}</p>
          <button @click="restartGame">重新开始</button>
        </div>
      </div>

      <div class="falling-words">
        <div
          v-for="word in fallingWords"
          :key="word.id"
          class="falling-word"
          :class="{ 'active-word': word.isActive, 'completed-word': word.completed }"
          :style="{
            left: word.x + 'px',
            top: word.y + 'px'
          }"
        >
          <div class="word-display">
            <span v-for="(letter, index) in word.display" :key="index"
                  :class="{ 'matched': letter.matched, 'missing': letter.missing }">
              {{ letter.char }}
            </span>
          </div>
          <div v-if="word.showMiss" class="miss-indicator">
            MISS!
          </div>

        </div>
      </div>

      <div class="input-display">
        当前输入: {{ currentInput }}
      </div>
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <div class="word-info">
        <h3>单词信息</h3>
        <div v-if="currentWord">
          <div class="word-text">{{ '?'.repeat(currentWord.word.length) }}</div>
          <div class="word-pos">{{ currentWord.pos }}</div>
          <div class="word-meaning">{{ currentWord.meaning }}</div>
        </div>
        <div v-else class="no-word">
          选择词库开始游戏
        </div>
      </div>

      <div class="recent-words">
        <h4>最近单词</h4>
        <div class="recent-list">
          <div v-for="(word, index) in recentWords" :key="index" class="recent-item">
            <span class="recent-word">{{ word.word }}</span>
            <span class="recent-meaning">{{ word.meaning }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'

interface WordData {
  word: string
  pos: string
  meaning: string
}

interface FallingWord {
  id: number
  word: string
  pos: string
  meaning: string
  x: number
  y: number
  speed: number
  display: Array<{ char: string, matched: boolean, missing: boolean }>
  missingIndex: number // 缺失字母的索引
  missingLetter: string // 缺失的字母
  showMiss: boolean // 是否显示miss
  missTimeout: ReturnType<typeof setTimeout> | null // miss显示的定时器
  isActive: boolean // 是否是当前活跃的单词（按下落顺序）
  completed: boolean
}

// 游戏状态
const gameStarted = ref(false)
const isPaused = ref(false)
const gameOver = ref(false)
const score = ref(0)
const hitCount = ref(0)
const escapeCount = ref(0)
const currentInput = ref('')
const selectedWordBank = ref('junior')

// 游戏数据
const fallingWords = ref<FallingWord[]>([])
const recentWords = ref<WordData[]>([])
const currentWord = ref<WordData | null>(null)
const gameArea = ref<HTMLElement>()

// 游戏配置
const gameConfig = {
  wordSpeed: 1,
  spawnRate: 2000, // 每2秒生成一个单词
  maxEscapes: 5
}

let gameLoop: ReturnType<typeof setInterval> | null = null
let wordSpawner: ReturnType<typeof setInterval> | null = null
let wordIdCounter = 0

// 计算命中率
const accuracy = computed(() => {
  const total = hitCount.value + escapeCount.value
  return total > 0 ? Math.round((hitCount.value / total) * 100) : 100
})

// 词库数据 (简化版本，实际应该从API获取)
const wordBanks = {
  junior: [
    { word: 'apple', pos: 'n.', meaning: '苹果' },
    { word: 'book', pos: 'n.', meaning: '书' },
    { word: 'cat', pos: 'n.', meaning: '猫' },
    { word: 'dog', pos: 'n.', meaning: '狗' },
    { word: 'eat', pos: 'v.', meaning: '吃' },
    { word: 'fish', pos: 'n.', meaning: '鱼' },
    { word: 'good', pos: 'adj.', meaning: '好的' },
    { word: 'happy', pos: 'adj.', meaning: '快乐的' },
    { word: 'house', pos: 'n.', meaning: '房子' },
    { word: 'love', pos: 'v.', meaning: '爱' }
  ],
  senior: [
    { word: 'beautiful', pos: 'adj.', meaning: '美丽的' },
    { word: 'computer', pos: 'n.', meaning: '电脑' },
    { word: 'education', pos: 'n.', meaning: '教育' },
    { word: 'important', pos: 'adj.', meaning: '重要的' },
    { word: 'knowledge', pos: 'n.', meaning: '知识' }
  ],
  cet4: [
    { word: 'achievement', pos: 'n.', meaning: '成就' },
    { word: 'challenge', pos: 'n.', meaning: '挑战' },
    { word: 'environment', pos: 'n.', meaning: '环境' },
    { word: 'opportunity', pos: 'n.', meaning: '机会' },
    { word: 'responsibility', pos: 'n.', meaning: '责任' }
  ],
  cet6: [
    { word: 'sophisticated', pos: 'adj.', meaning: '复杂的' },
    { word: 'phenomenon', pos: 'n.', meaning: '现象' },
    { word: 'comprehensive', pos: 'adj.', meaning: '综合的' },
    { word: 'inevitable', pos: 'adj.', meaning: '不可避免的' },
    { word: 'substantial', pos: 'adj.', meaning: '大量的' }
  ],
  postgraduate: [
    { word: 'sophisticated', pos: 'adj.', meaning: '精密的' },
    { word: 'paradigm', pos: 'n.', meaning: '范式' },
    { word: 'methodology', pos: 'n.', meaning: '方法论' },
    { word: 'hypothesis', pos: 'n.', meaning: '假设' },
    { word: 'empirical', pos: 'adj.', meaning: '经验的' }
  ]
}

// 游戏函数
const togglePause = () => {
  if (!gameStarted.value) return
  isPaused.value = !isPaused.value

  if (isPaused.value) {
    stopGameLoop()
  } else {
    startGameLoop()
  }
}

const restartGame = () => {
  stopGameLoop()
  gameStarted.value = false
  isPaused.value = false
  gameOver.value = false
  score.value = 0
  hitCount.value = 0
  escapeCount.value = 0
  currentInput.value = ''
  fallingWords.value = []
  recentWords.value = []
  currentWord.value = null

  // 重新聚焦游戏区域
  setTimeout(() => {
    gameArea.value?.focus()
  }, 100)
}

const changeWordBank = () => {
  if (gameStarted.value && !isPaused.value) return
  // 可以在这里重置游戏状态或保持当前状态
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (gameOver.value) return

  const key = event.key.toLowerCase()

  // 处理空格键开始游戏
  if (key === ' ' && !gameStarted.value) {
    startGame()
    event.preventDefault()
    return
  }

  // 只处理字母键
  if (key.match(/^[a-z]$/)) {
    if (!gameStarted.value) return // 必须先按空格开始

    if (!isPaused.value) {
      handleLetterInput(key)
    }
  }
}

const startGame = () => {
  gameStarted.value = true
  isPaused.value = false
  startGameLoop()
  gameArea.value?.focus()
}

const startGameLoop = () => {
  // 开始游戏循环
  gameLoop = setInterval(updateGame, 16) // 60 FPS

  // 开始生成单词
  wordSpawner = setInterval(spawnWord, gameConfig.spawnRate)

  // 立即生成第一个单词
  spawnWord()
}

const stopGameLoop = () => {
  if (gameLoop) {
    clearInterval(gameLoop)
    gameLoop = null
  }
  if (wordSpawner) {
    clearInterval(wordSpawner)
    wordSpawner = null
  }
}

const updateGame = () => {
  if (isPaused.value || gameOver.value) return

  // 更新下落单词位置
  fallingWords.value.forEach(word => {
    if (!word.completed) {
      word.y += word.speed
    }
  })

  // 检查逃脱的单词
  const escapedWords = fallingWords.value.filter(word => word.y > 600 && !word.completed)
  escapedWords.forEach(word => {
    escapeCount.value++
    score.value = Math.max(0, score.value - 10) // 逃脱扣10分

    // 清除miss定时器
    if (word.missTimeout) {
      clearTimeout(word.missTimeout)
      word.missTimeout = null
    }

    if (escapeCount.value >= gameConfig.maxEscapes) {
      endGame()
      return
    }
  })

  // 移除逃脱的单词
  fallingWords.value = fallingWords.value.filter(word => word.y <= 600)

  // 更新活跃单词
  updateActiveWord()
}

const spawnWord = () => {
  if (isPaused.value || gameOver.value) return

  const wordBank = wordBanks[selectedWordBank.value as keyof typeof wordBanks]
  const randomWord = wordBank[Math.floor(Math.random() * wordBank.length)]

  // 随机选择一个字母位置作为缺失字母
  const missingIndex = Math.floor(Math.random() * randomWord.word.length)
  const missingLetter = randomWord.word[missingIndex]

  const newWord: FallingWord = {
    id: wordIdCounter++,
    word: randomWord.word,
    pos: randomWord.pos,
    meaning: randomWord.meaning,
    x: Math.random() * 400, // 随机水平位置
    y: -50, // 从顶部开始
    speed: gameConfig.wordSpeed + Math.random() * 1, // 随机速度
    display: randomWord.word.split('').map((char, index) => ({
      char: index === missingIndex ? '_' : char, // 缺失位置显示下划线
      matched: false,
      missing: index === missingIndex
    })),
    missingIndex,
    missingLetter,
    showMiss: false,
    missTimeout: null,
    isActive: fallingWords.value.length === 0, // 第一个单词是活跃的
    completed: false
  }

  // 设置当前单词信息
  if (!currentWord.value || newWord.isActive) {
    currentWord.value = randomWord
  }

  fallingWords.value.push(newWord)

  // 更新活跃状态
  updateActiveWord()
}

// 处理字母输入的新逻辑
const handleLetterInput = (letter: string) => {
  // 找到当前活跃的单词（按下落顺序）
  const activeWord = getActiveWord()

  if (!activeWord) return

  // 检查输入的字母是否是缺失的字母
  if (letter === activeWord.missingLetter.toLowerCase()) {
    // 正确！完成单词
    completeWord(activeWord)
    currentInput.value = ''
  } else {
    // 错误！显示miss
    showMiss(activeWord)
    currentInput.value = letter // 显示当前输入
  }
}

// 获取当前活跃的单词（最上面的未完成单词）
const getActiveWord = (): FallingWord | null => {
  const activeWords = fallingWords.value
    .filter(word => !word.completed)
    .sort((a, b) => b.y - a.y) // 按Y坐标排序，最下面的优先

  return activeWords.length > 0 ? activeWords[0] : null
}

// 更新活跃单词状态
const updateActiveWord = () => {
  const activeWord = getActiveWord()

  fallingWords.value.forEach(word => {
    word.isActive = word === activeWord
  })

  // 更新当前单词信息
  if (activeWord) {
    currentWord.value = {
      word: activeWord.word,
      pos: activeWord.pos,
      meaning: activeWord.meaning
    }
  }
}

// 显示miss效果
const showMiss = (word: FallingWord) => {
  word.showMiss = true

  // 清除之前的定时器
  if (word.missTimeout) {
    clearTimeout(word.missTimeout)
  }

  // 1秒后隐藏miss
  word.missTimeout = setTimeout(() => {
    word.showMiss = false
    word.missTimeout = null
  }, 1000)
}

const completeWord = (word: FallingWord) => {
  word.completed = true
  hitCount.value++
  score.value += 10 // 完成单词得10分

  // 清除miss定时器
  if (word.missTimeout) {
    clearTimeout(word.missTimeout)
    word.missTimeout = null
  }

  // 恢复完整单词显示
  word.display[word.missingIndex].char = word.missingLetter
  word.display[word.missingIndex].matched = true
  word.display[word.missingIndex].missing = false

  // 只有答对了才添加到最近单词列表
  recentWords.value.unshift({
    word: word.word,
    pos: word.pos,
    meaning: word.meaning
  })

  // 保持最近单词列表最多10个
  if (recentWords.value.length > 10) {
    recentWords.value.pop()
  }

  // 延迟移除完成的单词，让玩家看到完整单词
  setTimeout(() => {
    fallingWords.value = fallingWords.value.filter(w => w.id !== word.id)
    updateActiveWord()
  }, 500)

  // 立即更新活跃单词
  updateActiveWord()
}

const endGame = () => {
  gameOver.value = true
  stopGameLoop()
}

// 生命周期
onMounted(() => {
  // 聚焦游戏区域以接收键盘事件
  gameArea.value?.focus()
})

onUnmounted(() => {
  stopGameLoop()
})
</script>

<style scoped>
.game-container {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  font-family: 'Arial', sans-serif;
}

/* 左侧面板 */
.left-panel {
  width: 250px;
  background: rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-right: 2px solid rgba(255, 255, 255, 0.1);
}

.score-section {
  margin-bottom: 30px;
}

.score-section h3 {
  margin: 0 0 15px 0;
  color: #ffd700;
  text-align: center;
}

.score {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
  color: #00ff00;
}

.stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.stats div {
  background: rgba(255, 255, 255, 0.1);
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 14px;
}

.accuracy {
  text-align: center;
  font-size: 16px;
  color: #87ceeb;
}

.controls {
  margin-bottom: 30px;
}

.controls button {
  width: 100%;
  padding: 12px;
  margin-bottom: 10px;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.controls button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.word-bank-section h4 {
  margin: 0 0 10px 0;
  color: #ffd700;
}

.word-bank-section select {
  width: 100%;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  font-size: 14px;
}

.word-bank-section select option {
  background: #2a5298;
  color: white;
}

/* 中间游戏区域 */
.game-area {
  flex: 1;
  position: relative;
  overflow: hidden;
  outline: none;
  background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
}

.game-header {
  text-align: center;
  padding: 20px;
}

.game-header h1 {
  margin: 0;
  font-size: 36px;
  color: #ffd700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.start-message {
  font-size: 18px;
  color: #87ceeb;
  margin-top: 10px;
}

.game-over {
  background: rgba(0, 0, 0, 0.8);
  padding: 30px;
  border-radius: 15px;
  margin: 50px auto;
  max-width: 300px;
}

.game-over h2 {
  color: #ff6b6b;
  margin: 0 0 15px 0;
}

.game-over button {
  padding: 12px 24px;
  background: linear-gradient(45deg, #00ff00, #32cd32);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
}

.falling-words {
  position: relative;
  height: 500px;
  margin: 20px;
}

.falling-word {
  position: absolute;
  font-size: 20px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  transition: all 0.3s ease;
  color: #ffffff;
}

.falling-word.active-word {
  color: #00ff00;
  transform: scale(1.1);
  filter: drop-shadow(0 0 10px #00ff00);
}

.falling-word.completed-word {
  color: #ffd700;
  filter: drop-shadow(0 0 8px #ffd700);
}

.word-display {
  margin-bottom: 5px;
}

.word-display span {
  display: inline-block;
  padding: 2px 4px;
  margin: 0 1px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
}

.word-display span.matched {
  background: rgba(0, 255, 0, 0.3);
  color: #00ff00;
}

.word-display span.missing {
  background: rgba(255, 255, 0, 0.3);
  color: #ffff00;
  border: 2px dashed #ffff00;
  animation: blink 1s infinite;
}

.miss-indicator {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 0, 0, 0.9);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 14px;
  font-weight: bold;
  animation: missShake 0.5s ease-in-out;
}



.input-display {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 18px;
  color: #00ff00;
  min-width: 200px;
  text-align: center;
}

/* 右侧面板 */
.right-panel {
  width: 300px;
  background: rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-left: 2px solid rgba(255, 255, 255, 0.1);
}

.word-info {
  margin-bottom: 30px;
}

.word-info h3 {
  margin: 0 0 15px 0;
  color: #ffd700;
  text-align: center;
}

.word-text {
  font-size: 24px;
  font-weight: bold;
  color: #00ff00;
  text-align: center;
  margin-bottom: 10px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.word-pos {
  font-size: 16px;
  color: #87ceeb;
  text-align: center;
  margin-bottom: 10px;
  font-style: italic;
}

.word-meaning {
  font-size: 18px;
  color: #ffffff;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
  border-radius: 8px;
  border-left: 4px solid #ffd700;
}

.no-word {
  text-align: center;
  color: #87ceeb;
  font-style: italic;
  padding: 20px;
}

.recent-words h4 {
  margin: 0 0 15px 0;
  color: #ffd700;
  text-align: center;
}

.recent-list {
  max-height: 400px;
  overflow-y: auto;
}

.recent-item {
  background: rgba(255, 255, 255, 0.1);
  margin-bottom: 8px;
  padding: 10px;
  border-radius: 6px;
  border-left: 3px solid #00ff00;
  transition: all 0.3s ease;
}

.recent-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.recent-word {
  display: block;
  font-weight: bold;
  color: #00ff00;
  font-size: 16px;
  margin-bottom: 4px;
}

.recent-meaning {
  display: block;
  color: #ffffff;
  font-size: 14px;
  opacity: 0.9;
}

/* 滚动条样式 */
.recent-list::-webkit-scrollbar {
  width: 6px;
}

.recent-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.recent-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.recent-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel, .right-panel {
    width: 200px;
  }
}

@media (max-width: 900px) {
  .game-container {
    flex-direction: column;
  }

  .left-panel, .right-panel {
    width: 100%;
    height: auto;
  }

  .game-area {
    height: 400px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.falling-word {
  animation: fadeIn 0.3s ease-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

@keyframes missShake {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  25% { transform: translateX(-50%) translateY(-5px); }
  75% { transform: translateX(-50%) translateY(5px); }
}

@keyframes wordComplete {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1) translateY(-20px); opacity: 0; }
}

.word-display span.matched {
  animation: pulse 0.3s ease-in-out;
}

.completed-word {
  animation: wordComplete 0.5s ease-out forwards;
}
</style>