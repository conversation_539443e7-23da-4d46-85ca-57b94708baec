<template>
  <div class="game-container">
    <!-- 左侧面板 -->
    <div class="left-panel">
      <div class="score-section">
        <h3>个人成绩</h3>
        <div class="score">得分: {{ score }}</div>
        <div class="stats">
          <div>击落: {{ hitCount }}</div>
          <div>逃脱: {{ escapeCount }}</div>
        </div>
        <div class="accuracy">命中率: {{ accuracy }}%</div>
      </div>

      <div class="controls">
        <button @click="togglePause" :disabled="!gameStarted">
          {{ isPaused ? '▶️ 开始' : '⏸️ 暂停' }}
        </button>
        <button @click="restartGame">🔄 重新游戏</button>
      </div>

      <div class="word-bank-section">
        <h4>选择词库:</h4>
        <select v-model="selectedWordBank" @change="changeWordBank" :disabled="gameStarted && !isPaused">
          <option value="junior">初中英语</option>
          <option value="senior">高中英语</option>
          <option value="cet4">大学四级</option>
          <option value="cet6">大学六级</option>
          <option value="postgraduate">考研英语</option>
        </select>
      </div>
    </div>

    <!-- 中间游戏区域 -->
    <div class="game-area" @keydown="handleKeyDown" tabindex="0" ref="gameArea">
      <div class="game-header">
        <h1>单词雷电</h1>
        <div v-if="!gameStarted" class="start-message">
          按任意字母键开始游戏
        </div>
        <div v-if="gameOver" class="game-over">
          <h2>游戏结束!</h2>
          <p>最终得分: {{ score }}</p>
          <button @click="restartGame">重新开始</button>
        </div>
      </div>

      <div class="falling-words">
        <div
          v-for="word in fallingWords"
          :key="word.id"
          class="falling-word"
          :style="{
            left: word.x + 'px',
            top: word.y + 'px',
            color: word.isMatching ? '#00ff00' : '#ffffff'
          }"
        >
          <div class="word-display">
            <span v-for="(letter, index) in word.display" :key="index"
                  :class="{ 'matched': letter.matched, 'missing': letter.missing }">
              {{ letter.char }}
            </span>
          </div>
          <div class="user-input" v-if="word.userInput">
            {{ word.userInput }}
          </div>
        </div>
      </div>

      <div class="input-display">
        当前输入: {{ currentInput }}
      </div>
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <div class="word-info">
        <h3>单词信息</h3>
        <div v-if="currentWord">
          <div class="word-text">{{ currentWord.word }}</div>
          <div class="word-pos">{{ currentWord.pos }}</div>
          <div class="word-meaning">{{ currentWord.meaning }}</div>
        </div>
        <div v-else class="no-word">
          选择词库开始游戏
        </div>
      </div>

      <div class="recent-words">
        <h4>最近单词</h4>
        <div class="recent-list">
          <div v-for="word in recentWords" :key="word.id" class="recent-item">
            <span class="recent-word">{{ word.word }}</span>
            <span class="recent-meaning">{{ word.meaning }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'

interface WordData {
  word: string
  pos: string
  meaning: string
}

interface FallingWord {
  id: number
  word: string
  pos: string
  meaning: string
  x: number
  y: number
  speed: number
  display: Array<{ char: string, matched: boolean, missing: boolean }>
  userInput: string
  isMatching: boolean
  completed: boolean
}

// 游戏状态
const gameStarted = ref(false)
const isPaused = ref(false)
const gameOver = ref(false)
const score = ref(0)
const hitCount = ref(0)
const escapeCount = ref(0)
const currentInput = ref('')
const selectedWordBank = ref('junior')

// 游戏数据
const fallingWords = ref<FallingWord[]>([])
const recentWords = ref<WordData[]>([])
const currentWord = ref<WordData | null>(null)
const gameArea = ref<HTMLElement>()

// 游戏配置
const gameConfig = {
  wordSpeed: 1,
  spawnRate: 2000, // 每2秒生成一个单词
  maxEscapes: 5
}

let gameLoop: ReturnType<typeof setInterval> | null = null
let wordSpawner: ReturnType<typeof setInterval> | null = null
let wordIdCounter = 0

// 计算命中率
const accuracy = computed(() => {
  const total = hitCount.value + escapeCount.value
  return total > 0 ? Math.round((hitCount.value / total) * 100) : 100
})

// 词库数据 (简化版本，实际应该从API获取)
const wordBanks = {
  junior: [
    { word: 'apple', pos: 'n.', meaning: '苹果' },
    { word: 'book', pos: 'n.', meaning: '书' },
    { word: 'cat', pos: 'n.', meaning: '猫' },
    { word: 'dog', pos: 'n.', meaning: '狗' },
    { word: 'eat', pos: 'v.', meaning: '吃' },
    { word: 'fish', pos: 'n.', meaning: '鱼' },
    { word: 'good', pos: 'adj.', meaning: '好的' },
    { word: 'happy', pos: 'adj.', meaning: '快乐的' },
    { word: 'house', pos: 'n.', meaning: '房子' },
    { word: 'love', pos: 'v.', meaning: '爱' }
  ],
  senior: [
    { word: 'beautiful', pos: 'adj.', meaning: '美丽的' },
    { word: 'computer', pos: 'n.', meaning: '电脑' },
    { word: 'education', pos: 'n.', meaning: '教育' },
    { word: 'important', pos: 'adj.', meaning: '重要的' },
    { word: 'knowledge', pos: 'n.', meaning: '知识' }
  ],
  cet4: [
    { word: 'achievement', pos: 'n.', meaning: '成就' },
    { word: 'challenge', pos: 'n.', meaning: '挑战' },
    { word: 'environment', pos: 'n.', meaning: '环境' },
    { word: 'opportunity', pos: 'n.', meaning: '机会' },
    { word: 'responsibility', pos: 'n.', meaning: '责任' }
  ],
  cet6: [
    { word: 'sophisticated', pos: 'adj.', meaning: '复杂的' },
    { word: 'phenomenon', pos: 'n.', meaning: '现象' },
    { word: 'comprehensive', pos: 'adj.', meaning: '综合的' },
    { word: 'inevitable', pos: 'adj.', meaning: '不可避免的' },
    { word: 'substantial', pos: 'adj.', meaning: '大量的' }
  ],
  postgraduate: [
    { word: 'sophisticated', pos: 'adj.', meaning: '精密的' },
    { word: 'paradigm', pos: 'n.', meaning: '范式' },
    { word: 'methodology', pos: 'n.', meaning: '方法论' },
    { word: 'hypothesis', pos: 'n.', meaning: '假设' },
    { word: 'empirical', pos: 'adj.', meaning: '经验的' }
  ]
}

// 游戏函数
const togglePause = () => {
  if (!gameStarted.value) return
  isPaused.value = !isPaused.value

  if (isPaused.value) {
    stopGameLoop()
  } else {
    startGameLoop()
  }
}

const restartGame = () => {
  stopGameLoop()
  gameStarted.value = false
  isPaused.value = false
  gameOver.value = false
  score.value = 0
  hitCount.value = 0
  escapeCount.value = 0
  currentInput.value = ''
  fallingWords.value = []
  recentWords.value = []
  currentWord.value = null

  // 重新聚焦游戏区域
  setTimeout(() => {
    gameArea.value?.focus()
  }, 100)
}

const changeWordBank = () => {
  if (gameStarted.value && !isPaused.value) return
  // 可以在这里重置游戏状态或保持当前状态
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (gameOver.value) return

  const key = event.key.toLowerCase()

  // 只处理字母键
  if (key.match(/^[a-z]$/)) {
    if (!gameStarted.value) {
      startGame()
    }

    if (!isPaused.value) {
      currentInput.value += key
      checkWordMatch()
    }
  }

  // 处理退格键
  if (key === 'backspace') {
    currentInput.value = currentInput.value.slice(0, -1)
    updateWordMatching()
  }

  // 处理回车键
  if (key === 'enter') {
    submitWord()
  }
}

const startGame = () => {
  gameStarted.value = true
  isPaused.value = false
  startGameLoop()
  gameArea.value?.focus()
}

const startGameLoop = () => {
  // 开始游戏循环
  gameLoop = setInterval(updateGame, 16) // 60 FPS

  // 开始生成单词
  wordSpawner = setInterval(spawnWord, gameConfig.spawnRate)

  // 立即生成第一个单词
  spawnWord()
}

const stopGameLoop = () => {
  if (gameLoop) {
    clearInterval(gameLoop)
    gameLoop = null
  }
  if (wordSpawner) {
    clearInterval(wordSpawner)
    wordSpawner = null
  }
}

const updateGame = () => {
  if (isPaused.value || gameOver.value) return

  // 更新下落单词位置
  fallingWords.value.forEach(word => {
    word.y += word.speed
  })

  // 检查逃脱的单词
  const escapedWords = fallingWords.value.filter(word => word.y > 600) // 假设游戏区域高度为600px
  escapedWords.forEach(word => {
    if (!word.completed) {
      escapeCount.value++
      score.value = Math.max(0, score.value - 10) // 逃脱扣10分

      if (escapeCount.value >= gameConfig.maxEscapes) {
        endGame()
        return
      }
    }
  })

  // 移除逃脱的单词
  fallingWords.value = fallingWords.value.filter(word => word.y <= 600)
}

const spawnWord = () => {
  if (isPaused.value || gameOver.value) return

  const wordBank = wordBanks[selectedWordBank.value as keyof typeof wordBanks]
  const randomWord = wordBank[Math.floor(Math.random() * wordBank.length)]

  const newWord: FallingWord = {
    id: wordIdCounter++,
    word: randomWord.word,
    pos: randomWord.pos,
    meaning: randomWord.meaning,
    x: Math.random() * 400, // 随机水平位置
    y: -50, // 从顶部开始
    speed: gameConfig.wordSpeed + Math.random() * 1, // 随机速度
    display: randomWord.word.split('').map(char => ({
      char,
      matched: false,
      missing: Math.random() < 0.3 // 30% 概率缺失字母
    })),
    userInput: '',
    isMatching: false,
    completed: false
  }

  // 设置当前单词信息
  if (!currentWord.value) {
    currentWord.value = randomWord
  }

  fallingWords.value.push(newWord)
}

const checkWordMatch = () => {
  updateWordMatching()

  // 检查是否完成单词
  fallingWords.value.forEach(word => {
    if (word.isMatching && currentInput.value.length === word.word.length) {
      if (currentInput.value === word.word) {
        completeWord(word)
      }
    }
  })
}

const updateWordMatching = () => {
  fallingWords.value.forEach(word => {
    const isMatching = word.word.startsWith(currentInput.value) && currentInput.value.length > 0
    word.isMatching = isMatching
    word.userInput = currentInput.value

    // 更新显示状态
    word.display.forEach((letter, index) => {
      if (index < currentInput.value.length) {
        letter.matched = currentInput.value[index] === letter.char
      } else {
        letter.matched = false
      }
    })
  })
}

const submitWord = () => {
  const matchingWord = fallingWords.value.find(word =>
    word.isMatching && currentInput.value === word.word
  )

  if (matchingWord) {
    completeWord(matchingWord)
  }

  currentInput.value = ''
  updateWordMatching()
}

const completeWord = (word: FallingWord) => {
  word.completed = true
  hitCount.value++
  score.value += 10 // 完成单词得10分

  // 添加到最近单词列表
  recentWords.value.unshift({
    word: word.word,
    pos: word.pos,
    meaning: word.meaning
  })

  // 保持最近单词列表最多10个
  if (recentWords.value.length > 10) {
    recentWords.value.pop()
  }

  // 移除完成的单词
  fallingWords.value = fallingWords.value.filter(w => w.id !== word.id)

  // 清空输入
  currentInput.value = ''

  // 更新当前单词信息为下一个单词
  if (fallingWords.value.length > 0) {
    const nextWord = fallingWords.value[0]
    currentWord.value = {
      word: nextWord.word,
      pos: nextWord.pos,
      meaning: nextWord.meaning
    }
  } else {
    currentWord.value = null
  }

  updateWordMatching()
}

const endGame = () => {
  gameOver.value = true
  stopGameLoop()
}

// 生命周期
onMounted(() => {
  // 聚焦游戏区域以接收键盘事件
  gameArea.value?.focus()
})

onUnmounted(() => {
  stopGameLoop()
})
</script>

<style scoped>
.game-container {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  font-family: 'Arial', sans-serif;
}

/* 左侧面板 */
.left-panel {
  width: 250px;
  background: rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-right: 2px solid rgba(255, 255, 255, 0.1);
}

.score-section {
  margin-bottom: 30px;
}

.score-section h3 {
  margin: 0 0 15px 0;
  color: #ffd700;
  text-align: center;
}

.score {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
  color: #00ff00;
}

.stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.stats div {
  background: rgba(255, 255, 255, 0.1);
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 14px;
}

.accuracy {
  text-align: center;
  font-size: 16px;
  color: #87ceeb;
}

.controls {
  margin-bottom: 30px;
}

.controls button {
  width: 100%;
  padding: 12px;
  margin-bottom: 10px;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.controls button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.word-bank-section h4 {
  margin: 0 0 10px 0;
  color: #ffd700;
}

.word-bank-section select {
  width: 100%;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  font-size: 14px;
}

.word-bank-section select option {
  background: #2a5298;
  color: white;
}

/* 中间游戏区域 */
.game-area {
  flex: 1;
  position: relative;
  overflow: hidden;
  outline: none;
  background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.3) 100%);
}

.game-header {
  text-align: center;
  padding: 20px;
}

.game-header h1 {
  margin: 0;
  font-size: 36px;
  color: #ffd700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.start-message {
  font-size: 18px;
  color: #87ceeb;
  margin-top: 10px;
}

.game-over {
  background: rgba(0, 0, 0, 0.8);
  padding: 30px;
  border-radius: 15px;
  margin: 50px auto;
  max-width: 300px;
}

.game-over h2 {
  color: #ff6b6b;
  margin: 0 0 15px 0;
}

.game-over button {
  padding: 12px 24px;
  background: linear-gradient(45deg, #00ff00, #32cd32);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
}

.falling-words {
  position: relative;
  height: 500px;
  margin: 20px;
}

.falling-word {
  position: absolute;
  font-size: 20px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  transition: color 0.3s ease;
}

.word-display {
  margin-bottom: 5px;
}

.word-display span {
  display: inline-block;
  padding: 2px 4px;
  margin: 0 1px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
}

.word-display span.matched {
  background: rgba(0, 255, 0, 0.3);
  color: #00ff00;
}

.word-display span.missing {
  background: rgba(255, 0, 0, 0.3);
  color: #ff6b6b;
}

.user-input {
  font-size: 14px;
  color: #87ceeb;
  text-align: center;
}

.input-display {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 18px;
  color: #00ff00;
  min-width: 200px;
  text-align: center;
}

/* 右侧面板 */
.right-panel {
  width: 300px;
  background: rgba(0, 0, 0, 0.3);
  padding: 20px;
  border-left: 2px solid rgba(255, 255, 255, 0.1);
}

.word-info {
  margin-bottom: 30px;
}

.word-info h3 {
  margin: 0 0 15px 0;
  color: #ffd700;
  text-align: center;
}

.word-text {
  font-size: 24px;
  font-weight: bold;
  color: #00ff00;
  text-align: center;
  margin-bottom: 10px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.word-pos {
  font-size: 16px;
  color: #87ceeb;
  text-align: center;
  margin-bottom: 10px;
  font-style: italic;
}

.word-meaning {
  font-size: 18px;
  color: #ffffff;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
  border-radius: 8px;
  border-left: 4px solid #ffd700;
}

.no-word {
  text-align: center;
  color: #87ceeb;
  font-style: italic;
  padding: 20px;
}

.recent-words h4 {
  margin: 0 0 15px 0;
  color: #ffd700;
  text-align: center;
}

.recent-list {
  max-height: 400px;
  overflow-y: auto;
}

.recent-item {
  background: rgba(255, 255, 255, 0.1);
  margin-bottom: 8px;
  padding: 10px;
  border-radius: 6px;
  border-left: 3px solid #00ff00;
  transition: all 0.3s ease;
}

.recent-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.recent-word {
  display: block;
  font-weight: bold;
  color: #00ff00;
  font-size: 16px;
  margin-bottom: 4px;
}

.recent-meaning {
  display: block;
  color: #ffffff;
  font-size: 14px;
  opacity: 0.9;
}

/* 滚动条样式 */
.recent-list::-webkit-scrollbar {
  width: 6px;
}

.recent-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.recent-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.recent-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel, .right-panel {
    width: 200px;
  }
}

@media (max-width: 900px) {
  .game-container {
    flex-direction: column;
  }

  .left-panel, .right-panel {
    width: 100%;
    height: auto;
  }

  .game-area {
    height: 400px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.falling-word {
  animation: fadeIn 0.3s ease-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.word-display span.matched {
  animation: pulse 0.3s ease-in-out;
}
</style>